<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Models\OrderPromotion;
use App\Models\ProductOffer;
use Spatie\LaravelData\Data;

final class OrderPromotionData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $type,
        public readonly ?VendorData $vendor,
        public readonly ?array $triggeringItems,
        public readonly ?array $appliedRules,
        public readonly ?array $appliedBenefits,
    ) {}

    public static function fromModel(OrderPromotion $orderPromotion): self
    {
        $vendor = $orderPromotion->promotion->vendor;
        $vendorData = $vendor ? new VendorData(
            id: $vendor->id,
            name: $vendor->name,
            imageUrl: asset("storage/{$vendor->image_path}"),
        ) : null;

        // Enrich triggering items with current product offer data
        $enrichedTriggeringItems = self::enrichTriggeringItems($orderPromotion->triggering_items ?? []);

        // Enrich applied benefits with current product offer data
        $enrichedAppliedBenefits = self::enrichAppliedBenefits($orderPromotion->applied_benefits ?? []);

        return self::from([
            'id' => $orderPromotion->id,
            'name' => $orderPromotion->promotion->name,
            'type' => $orderPromotion->promotion->type->value,
            'vendor' => $vendorData,
            'triggeringItems' => $enrichedTriggeringItems,
            'appliedRules' => $orderPromotion->applied_rules,
            'appliedBenefits' => $enrichedAppliedBenefits,
        ]);
    }

    /**
     * Enrich triggering items with current product offer data
     */
    private static function enrichTriggeringItems(array $triggeringItems): array
    {
        if (empty($triggeringItems)) {
            return $triggeringItems;
        }

        // Collect all product offer IDs to avoid N+1 queries
        $productOfferIds = collect($triggeringItems)
            ->pluck('product_offer_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();

        if (empty($productOfferIds)) {
            return $triggeringItems;
        }

        // Load all product offers in a single query
        $productOffers = ProductOffer::whereIn('id', $productOfferIds)
            ->get()
            ->keyBy('id');

        // Enrich each triggering item
        return collect($triggeringItems)->map(function ($item) use ($productOffers) {
            $productOfferId = $item['product_offer_id'] ?? null;

            if ($productOfferId && $productOffers->has($productOfferId)) {
                $productOffer = $productOffers->get($productOfferId);

                // Add missing fields if they don't exist
                $item['product_name'] = $item['product_name'] ?? $productOffer->name;
                $item['size'] = $item['size'] ?? $productOffer->size;
                $item['unit_of_measure'] = $item['unit_of_measure'] ?? $productOffer->unit_of_measure;
            }

            return $item;
        })->toArray();
    }

    /**
     * Enrich applied benefits with current product offer data
     */
    private static function enrichAppliedBenefits(array $appliedBenefits): array
    {
        if (empty($appliedBenefits)) {
            return $appliedBenefits;
        }

        // Collect all product offer IDs from benefits
        $productOfferIds = collect($appliedBenefits)
            ->pluck('product_offer_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();

        if (empty($productOfferIds)) {
            return $appliedBenefits;
        }

        // Load all product offers in a single query
        $productOffers = ProductOffer::whereIn('id', $productOfferIds)
            ->get()
            ->keyBy('id');

        // Enrich each applied benefit
        return collect($appliedBenefits)->map(function ($benefit) use ($productOffers) {
            $productOfferId = $benefit['product_offer_id'] ?? null;

            if ($productOfferId && $productOffers->has($productOfferId)) {
                $productOffer = $productOffers->get($productOfferId);

                // Enrich the product_offer data if it exists
                if (isset($benefit['product_offer']) && is_array($benefit['product_offer'])) {
                    $benefit['product_offer']['size'] = $benefit['product_offer']['size'] ?? $productOffer->size;
                    $benefit['product_offer']['unitOfMeasure'] = $benefit['product_offer']['unitOfMeasure'] ?? $productOffer->unit_of_measure;
                } else {
                    // Create product_offer data if it doesn't exist
                    $benefit['product_offer'] = [
                        'id' => $productOffer->id,
                        'name' => $productOffer->name,
                        'price' => $productOffer->price ? round($productOffer->price / 100, 2) : null,
                        'clinicPrice' => null, // We don't have clinic context here
                        'size' => $productOffer->size,
                        'unitOfMeasure' => $productOffer->unit_of_measure,
                    ];
                }
            }

            return $benefit;
        })->toArray();
    }
}
