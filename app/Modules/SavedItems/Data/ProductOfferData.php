<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\ProductStockStatus;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\Order\Data\ProductData;
use App\Modules\Order\Data\VendorData;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class ProductOfferData extends Data
{
    public function __construct(
        public string $id,
        public VendorData $vendor,
        public ProductData $product,
        public string $vendorSku,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public ?Money $price,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public ?Money $clinicPrice,
        public ProductStockStatus $stockStatus,
        public int $increments = 1,
        public bool $isRecommended = false,
        public ?string $size = null,
        public ?string $unitOfMeasure = null,
    ) {}

    public static function fromModel(ProductOffer $productOffer, Clinic $clinic): self
    {
        $clinicPrice = $productOffer->clinics->where('id', $clinic->id)->first()?->pivot?->price;

        $productOffers = $productOffer->product->productOffers()
            ->active()
            ->get();

        $offers = $productOffers->map(function ($offer) {
            return [
                'id' => $offer->id,
                'unitOfMeasure' => $offer->unit_of_measure,
                'size' => $offer->size,
            ];
        })->toArray();

        return new self(
            id: $productOffer->id,
            vendor: VendorData::from($productOffer->vendor),
            product: ProductData::from([
                'id' => $productOffer->product->id,
                'name' => $productOffer->product->name,
                'imageUrl' => $productOffer->product->image_url,
                'offers' => $offers,
            ]),
            vendorSku: $productOffer->vendor_sku,
            price: $productOffer->price ? Money::ofMinor($productOffer->price, 'USD') : null,
            clinicPrice: $clinicPrice ? Money::ofMinor($clinicPrice, 'USD') : null,
            stockStatus: $productOffer->stock_status,
            increments: $productOffer->increments ?? 1,
            isRecommended: $productOffer->is_recommended ?? false,
            size: $productOffer->size,
            unitOfMeasure: $productOffer->unit_of_measure,
        );
    }
}
