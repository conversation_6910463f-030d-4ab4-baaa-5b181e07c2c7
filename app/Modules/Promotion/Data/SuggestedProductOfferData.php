<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\ProductStockStatus;
use App\Http\Resources\Cart\Product as ProductData;
use App\Models\Product;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\Hidden;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class SuggestedProductOfferData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly ?string $imageUrl,
        public readonly ?string $vendorSku,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $price,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $clinicPrice,
        public readonly ProductStockStatus $stockStatus,
        public readonly VendorData $vendor,
        public readonly ?int $orderItemsSumTotalPrice,
        public readonly ?int $increments,
        #[Hidden]
        public readonly ?Product $product,
        public readonly bool $isRecommended = false,
        public readonly ?string $rebatePercent = null,
        public readonly ?string $size = null,
        public readonly ?string $unitOfMeasure = null,
    ) {}

    public function with(): array
    {
        return [
            'product' => $this->product ? new ProductData($this->product) : null,
        ];
    }
}
