<?php

declare(strict_types=1);

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Exceptions\ApiException;
use App\Modules\Order\Services\Vendor\Http\ResponseHandler;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Str;

describe('ResponseHandler Recent Changes', function () {
    beforeEach(function () {
        $this->session = IntegrationSession::factory()->create();
        $this->subject = (object) ['id' => (string) Str::uuid()];
        $this->responseHandler = new ResponseHandler();
    });

    it('logs error events and throws ApiException when handleException is called with transport exception', function () {
        // Arrange
        $transportException = new ConnectionException('Connection timeout');
        $integrationEventType = 'test_api_call';
        $endpoint = '/api/test';
        $method = 'POST';

        // Act & Assert
        expect(fn () => $this->responseHandler->handleException(
            $transportException,
            $this->session,
            $integrationEventType,
            $this->subject,
            $endpoint,
            $method
        ))->toThrow(ApiException::class, 'test_api_call POST to /api/test failed');

        // Verify that an error event was logged
        $this->session->refresh();
        $errorEvents = $this->session->events()->where('status', 'error')->get();
        
        expect($errorEvents)->toHaveCount(1);
        
        $errorEvent = $errorEvents->first();
        expect($errorEvent->action)->toBe($integrationEventType)
            ->and($errorEvent->metadata['endpoint'])->toBe($endpoint)
            ->and($errorEvent->metadata['method'])->toBe($method)
            ->and($errorEvent->metadata['response']['exception'])->toBe(ConnectionException::class)
            ->and($errorEvent->metadata['response']['message'])->toBe('Connection timeout');
    });

    it('properly handles callable execution through handleCall method with exception logging', function () {
        // Arrange
        $integrationEventType = 'test_api_call';
        $endpoint = '/api/test';
        $method = 'GET';
        
        // Create a callable that throws an exception
        $failingCallable = function () {
            throw new \RuntimeException('Network error occurred');
        };

        // Act & Assert
        expect(fn () => $this->responseHandler->handleCall(
            $failingCallable,
            $this->session,
            $integrationEventType,
            $this->subject,
            $endpoint,
            $method
        ))->toThrow(ApiException::class, 'test_api_call GET to /api/test failed');

        // Verify that an error event was logged with correct exception details
        $this->session->refresh();
        $errorEvents = $this->session->events()->where('status', 'error')->get();
        
        expect($errorEvents)->toHaveCount(1);
        
        $errorEvent = $errorEvents->first();
        expect($errorEvent->action)->toBe($integrationEventType)
            ->and($errorEvent->metadata['endpoint'])->toBe($endpoint)
            ->and($errorEvent->metadata['method'])->toBe($method)
            ->and($errorEvent->metadata['response']['exception'])->toBe(\RuntimeException::class)
            ->and($errorEvent->metadata['response']['message'])->toBe('Network error occurred');
    });
});
