<?php

declare(strict_types=1);

use App\Models\Order;
use App\Models\OrderPromotion;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionRule;
use App\Modules\Promotion\Models\PromotionAction;
use App\Modules\Promotion\Models\PromotionCondition;
use App\Modules\Gpo\Models\GpoAccount;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->clinic = Clinic::factory()->create();
    $this->vendor = Vendor::factory()->create();
    $this->product = Product::factory()->create();
    $this->gpoAccount = GpoAccount::factory()->create();

    $this->productOffer = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor->id,
        'product_id' => $this->product->id,
        'name' => 'Test Product with UOM',
        'size' => '500ml',
        'unit_of_measure' => 'bottles',
        'price' => 2000, // $20.00
    ]);

    $this->order = Order::factory()->create([
        'clinic_id' => $this->clinic->id,
    ]);

    $this->promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
        'type' => PromotionType::BuyXGetY,
        'name' => 'Test Promotion',
    ]);

    $this->promotion->productOffers()->attach($this->productOffer->id);

    $rule = PromotionRule::factory()->create([
        'promotion_id' => $this->promotion->id,
        'priority' => 1,
    ]);

    PromotionCondition::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ConditionType::MinimumQuantity,
        'config' => ['quantity' => 5],
    ]);

    PromotionAction::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ActionType::GiveFreeProduct,
        'config' => [
            'free_product_offer_id' => $this->productOffer->id,
            'quantity' => 1,
        ],
    ]);

    // Create an OrderPromotion with unit of measure data
    OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => $this->productOffer->id,
                'product_id' => $this->product->id,
                'product_name' => 'Test Product with UOM',
                'quantity' => 6,
                'size' => '500ml',
                'unit_of_measure' => 'bottles',
            ],
        ],
        'applied_rules' => [
            [
                'rule_id' => $rule->id,
                'priority' => 1,
                'conditions' => [],
                'actions' => [],
            ],
        ],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $this->productOffer->id,
                'product_offer' => [
                    'id' => $this->productOffer->id,
                    'name' => 'Test Product with UOM',
                    'price' => 20.00,
                    'clinicPrice' => null,
                    'size' => '500ml',
                    'unitOfMeasure' => 'bottles',
                ],
                'quantity' => 1,
                'message' => null,
            ],
        ],
    ]);
});

it('includes unitOfMeasure and size in order promotion response via OrderData', function () {
    $this->actingAs($this->user);

    $response = $this->getJson("/api/orders/{$this->order->id}");

    $response->assertOk();

    $promotions = $response->json('promotions');
    expect($promotions)->toBeArray();
    expect($promotions)->toHaveCount(1);

    $promotion = $promotions[0];
    expect($promotion)->toHaveKey('triggeringItems');
    expect($promotion)->toHaveKey('appliedBenefits');

    // Check triggering items include unit of measure and size
    $triggeringItems = $promotion['triggeringItems'];
    expect($triggeringItems)->toBeArray();
    expect($triggeringItems[0])->toHaveKey('size');
    expect($triggeringItems[0])->toHaveKey('unit_of_measure');
    expect($triggeringItems[0]['size'])->toBe('500ml');
    expect($triggeringItems[0]['unit_of_measure'])->toBe('bottles');
    expect($triggeringItems[0]['product_name'])->toBe('Test Product with UOM');

    // Check applied benefits include unit of measure and size
    $appliedBenefits = $promotion['appliedBenefits'];
    expect($appliedBenefits)->toBeArray();
    expect($appliedBenefits[0])->toHaveKey('product_offer');
    expect($appliedBenefits[0]['product_offer'])->toHaveKey('size');
    expect($appliedBenefits[0]['product_offer'])->toHaveKey('unitOfMeasure');
    expect($appliedBenefits[0]['product_offer']['size'])->toBe('500ml');
    expect($appliedBenefits[0]['product_offer']['unitOfMeasure'])->toBe('bottles');
});

it('handles null unitOfMeasure and size in order promotion response', function () {
    // Create a product offer without size and unit of measure
    $productOfferWithoutUOM = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor->id,
        'product_id' => Product::factory()->create()->id,
        'name' => 'Product without UOM',
        'size' => null,
        'unit_of_measure' => null,
    ]);

    // Update the order promotion to use the product without UOM
    $orderPromotion = $this->order->promotions()->first();
    $orderPromotion->update([
        'triggering_items' => [
            [
                'product_offer_id' => $productOfferWithoutUOM->id,
                'product_id' => $productOfferWithoutUOM->product_id,
                'product_name' => 'Product without UOM',
                'quantity' => 3,
                'size' => null,
                'unit_of_measure' => null,
            ],
        ],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $productOfferWithoutUOM->id,
                'product_offer' => [
                    'id' => $productOfferWithoutUOM->id,
                    'name' => 'Product without UOM',
                    'price' => 15.00,
                    'clinicPrice' => null,
                    'size' => null,
                    'unitOfMeasure' => null,
                ],
                'quantity' => 1,
                'message' => null,
            ],
        ],
    ]);

    $this->actingAs($this->user);

    $response = $this->getJson("/api/orders/{$this->order->id}");

    $response->assertOk();

    $promotions = $response->json('promotions');
    $promotion = $promotions[0];

    // Check that null values are properly handled
    $triggeringItems = $promotion['triggeringItems'];
    expect($triggeringItems[0]['size'])->toBeNull();
    expect($triggeringItems[0]['unit_of_measure'])->toBeNull();

    $appliedBenefits = $promotion['appliedBenefits'];
    expect($appliedBenefits[0]['product_offer']['size'])->toBeNull();
    expect($appliedBenefits[0]['product_offer']['unitOfMeasure'])->toBeNull();
});
