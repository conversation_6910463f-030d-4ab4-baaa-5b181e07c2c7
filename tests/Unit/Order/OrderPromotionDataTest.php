<?php

declare(strict_types=1);

use App\Models\Order;
use App\Models\OrderPromotion;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Clinic;
use App\Modules\Order\Data\OrderPromotionData;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Gpo\Models\GpoAccount;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create();
    $this->product = Product::factory()->create();
    $this->clinic = Clinic::factory()->create();
    $this->gpoAccount = GpoAccount::factory()->create();
    $this->order = Order::factory()->create(['clinic_id' => $this->clinic->id]);

    $this->productOffer = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor->id,
        'product_id' => $this->product->id,
        'name' => 'Test Product with UOM',
        'size' => '500ml',
        'unit_of_measure' => 'bottles',
        'price' => 2000, // $20.00
    ]);

    $this->promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
        'type' => PromotionType::BuyXGetY,
        'name' => 'Test Promotion',
    ]);
});

it('enriches triggering items with missing unitOfMeasure and size for existing order promotions', function () {
    // Create an order promotion with old format (missing size and unit_of_measure)
    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => $this->productOffer->id,
                'product_id' => $this->product->id,
                'quantity' => 6,
                // Missing: product_name, size, unit_of_measure
            ],
        ],
        'applied_rules' => [],
        'applied_benefits' => [],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    expect($orderPromotionData->triggeringItems)->toBeArray();
    expect($orderPromotionData->triggeringItems[0])->toHaveKey('size');
    expect($orderPromotionData->triggeringItems[0])->toHaveKey('unit_of_measure');
    expect($orderPromotionData->triggeringItems[0])->toHaveKey('product_name');
    expect($orderPromotionData->triggeringItems[0]['size'])->toBe('500ml');
    expect($orderPromotionData->triggeringItems[0]['unit_of_measure'])->toBe('bottles');
    expect($orderPromotionData->triggeringItems[0]['product_name'])->toBe('Test Product with UOM');
});

it('enriches applied benefits with missing unitOfMeasure and size for existing order promotions', function () {
    // Create an order promotion with old format applied benefits
    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [],
        'applied_rules' => [],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $this->productOffer->id,
                'quantity' => 1,
                'message' => null,
                // Missing: product_offer details
            ],
        ],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    expect($orderPromotionData->appliedBenefits)->toBeArray();
    expect($orderPromotionData->appliedBenefits[0])->toHaveKey('product_offer');
    expect($orderPromotionData->appliedBenefits[0]['product_offer'])->toHaveKey('size');
    expect($orderPromotionData->appliedBenefits[0]['product_offer'])->toHaveKey('unitOfMeasure');
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['size'])->toBe('500ml');
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['unitOfMeasure'])->toBe('bottles');
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['name'])->toBe('Test Product with UOM');
});

it('preserves existing unitOfMeasure and size data when already present', function () {
    // Create an order promotion with new format (already has size and unit_of_measure)
    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => $this->productOffer->id,
                'product_id' => $this->product->id,
                'product_name' => 'Original Product Name',
                'quantity' => 6,
                'size' => 'Original Size',
                'unit_of_measure' => 'Original UOM',
            ],
        ],
        'applied_rules' => [],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $this->productOffer->id,
                'product_offer' => [
                    'id' => $this->productOffer->id,
                    'name' => 'Original Free Product',
                    'price' => 15.00,
                    'clinicPrice' => null,
                    'size' => 'Original Free Size',
                    'unitOfMeasure' => 'Original Free UOM',
                ],
                'quantity' => 1,
                'message' => null,
            ],
        ],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    // Should preserve original data, not overwrite with current product offer data
    expect($orderPromotionData->triggeringItems[0]['size'])->toBe('Original Size');
    expect($orderPromotionData->triggeringItems[0]['unit_of_measure'])->toBe('Original UOM');
    expect($orderPromotionData->triggeringItems[0]['product_name'])->toBe('Original Product Name');

    expect($orderPromotionData->appliedBenefits[0]['product_offer']['size'])->toBe('Original Free Size');
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['unitOfMeasure'])->toBe('Original Free UOM');
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['name'])->toBe('Original Free Product');
});

it('handles null unitOfMeasure and size values correctly', function () {
    $productOfferWithoutUOM = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor->id,
        'product_id' => Product::factory()->create()->id,
        'name' => 'Product without UOM',
        'size' => null,
        'unit_of_measure' => null,
    ]);

    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => $productOfferWithoutUOM->id,
                'product_id' => $productOfferWithoutUOM->product_id,
                'quantity' => 3,
            ],
        ],
        'applied_rules' => [],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $productOfferWithoutUOM->id,
                'quantity' => 1,
            ],
        ],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    expect($orderPromotionData->triggeringItems[0]['size'])->toBeNull();
    expect($orderPromotionData->triggeringItems[0]['unit_of_measure'])->toBeNull();
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['size'])->toBeNull();
    expect($orderPromotionData->appliedBenefits[0]['product_offer']['unitOfMeasure'])->toBeNull();
});

it('handles empty triggering items and applied benefits arrays', function () {
    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [],
        'applied_rules' => [],
        'applied_benefits' => [],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    expect($orderPromotionData->triggeringItems)->toBe([]);
    expect($orderPromotionData->appliedBenefits)->toBe([]);
});

it('handles missing product offers gracefully', function () {
    $nonExistentProductOfferId = 'non-existent-id';

    $orderPromotion = OrderPromotion::factory()->create([
        'order_id' => $this->order->id,
        'promotion_id' => $this->promotion->id,
        'triggering_items' => [
            [
                'product_offer_id' => $nonExistentProductOfferId,
                'product_id' => $this->product->id,
                'quantity' => 6,
            ],
        ],
        'applied_rules' => [],
        'applied_benefits' => [
            [
                'type' => ActionType::GiveFreeProduct->value,
                'product_offer_id' => $nonExistentProductOfferId,
                'quantity' => 1,
            ],
        ],
    ]);

    $orderPromotionData = OrderPromotionData::fromModel($orderPromotion);

    // Should not crash and should return original data
    expect($orderPromotionData->triggeringItems)->toBeArray();
    expect($orderPromotionData->appliedBenefits)->toBeArray();
    expect($orderPromotionData->triggeringItems[0]['product_offer_id'])->toBe($nonExistentProductOfferId);
    expect($orderPromotionData->appliedBenefits[0]['product_offer_id'])->toBe($nonExistentProductOfferId);
});
