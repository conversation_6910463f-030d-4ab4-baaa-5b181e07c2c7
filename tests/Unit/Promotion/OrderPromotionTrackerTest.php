<?php

declare(strict_types=1);

use App\Models\Order;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Clinic;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionRule;
use App\Modules\Promotion\Models\PromotionAction;
use App\Modules\Promotion\Models\PromotionCondition;
use App\Modules\Promotion\Services\OrderPromotionTracker;
use App\Modules\Gpo\Models\GpoAccount;

it('includes unitOfMeasure and size in triggering items', function () {
    $vendor = Vendor::factory()->create();
    $product = Product::factory()->create();
    $clinic = Clinic::factory()->create();
    $order = Order::factory()->create(['clinic_id' => $clinic->id]);
    $gpoAccount = GpoAccount::factory()->create();

    $productOffer = ProductOffer::factory()->create([
        'vendor_id' => $vendor->id,
        'product_id' => $product->id,
        'name' => 'Test Product Offer',
        'size' => '500ml',
        'unit_of_measure' => 'bottles',
    ]);

    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $vendor->id,
        'type' => PromotionType::BuyXGetY,
    ]);

    $promotion->productOffers()->attach($productOffer->id);

    $rule = PromotionRule::factory()->create([
        'promotion_id' => $promotion->id,
        'priority' => 1,
    ]);

    PromotionCondition::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ConditionType::MinimumQuantity,
        'config' => ['quantity' => 5],
    ]);

    PromotionAction::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ActionType::GiveFreeProduct,
        'config' => [
            'free_product_offer_id' => $productOffer->id,
            'quantity' => 1,
        ],
    ]);

    // Create context with cart data
    $context = new Context($clinic->id, [$product->id => 6]);

    $tracker = new OrderPromotionTracker();
    $appliedPromotions = collect([
        [
            'promotion' => $promotion,
            'rule' => $rule,
        ]
    ]);

    $tracker->trackAppliedPromotions($order, $appliedPromotions, $context);

    $orderPromotion = $order->promotions()->first();

    expect($orderPromotion)->not->toBeNull();
    expect($orderPromotion->triggering_items)->toBeArray();
    expect($orderPromotion->triggering_items[0])->toHaveKey('size');
    expect($orderPromotion->triggering_items[0])->toHaveKey('unit_of_measure');
    expect($orderPromotion->triggering_items[0]['size'])->toBe('500ml');
    expect($orderPromotion->triggering_items[0]['unit_of_measure'])->toBe('bottles');
    expect($orderPromotion->triggering_items[0]['product_name'])->toBe('Test Product Offer');
});

it('includes unitOfMeasure and size in applied benefits for free products', function () {
    $vendor = Vendor::factory()->create();
    $product = Product::factory()->create();
    $clinic = Clinic::factory()->create();
    $order = Order::factory()->create(['clinic_id' => $clinic->id]);
    $gpoAccount = GpoAccount::factory()->create();

    $freeProductOffer = ProductOffer::factory()->create([
        'vendor_id' => $vendor->id,
        'product_id' => $product->id,
        'name' => 'Free Product Offer',
        'size' => '250mg',
        'unit_of_measure' => 'tablets',
        'price' => 1000, // $10.00
    ]);

    $triggeringProductOffer = ProductOffer::factory()->create([
        'vendor_id' => $vendor->id,
        'product_id' => Product::factory()->create()->id,
        'name' => 'Triggering Product',
        'size' => '1L',
        'unit_of_measure' => 'bottles',
    ]);

    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $vendor->id,
        'type' => PromotionType::BuyXGetY,
    ]);

    $promotion->productOffers()->attach($triggeringProductOffer->id);

    $rule = PromotionRule::factory()->create([
        'promotion_id' => $promotion->id,
        'priority' => 1,
    ]);

    PromotionCondition::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ConditionType::MinimumQuantity,
        'config' => ['quantity' => 5],
    ]);

    PromotionAction::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ActionType::GiveFreeProduct,
        'config' => [
            'free_product_offer_id' => $freeProductOffer->id,
            'quantity' => 1,
        ],
    ]);

    // Create context with cart data
    $context = new Context($clinic->id, [$triggeringProductOffer->product_id => 6]);

    $tracker = new OrderPromotionTracker();
    $appliedPromotions = collect([
        [
            'promotion' => $promotion,
            'rule' => $rule,
        ]
    ]);

    $tracker->trackAppliedPromotions($order, $appliedPromotions, $context);

    $orderPromotion = $order->promotions()->first();

    expect($orderPromotion)->not->toBeNull();
    expect($orderPromotion->applied_benefits)->toBeArray();
    expect($orderPromotion->applied_benefits[0])->toHaveKey('product_offer');
    expect($orderPromotion->applied_benefits[0]['product_offer'])->toHaveKey('size');
    expect($orderPromotion->applied_benefits[0]['product_offer'])->toHaveKey('unitOfMeasure');
    expect($orderPromotion->applied_benefits[0]['product_offer']['size'])->toBe('250mg');
    expect($orderPromotion->applied_benefits[0]['product_offer']['unitOfMeasure'])->toBe('tablets');
    expect($orderPromotion->applied_benefits[0]['product_offer']['name'])->toBe('Free Product Offer');
});

it('handles null unitOfMeasure and size values', function () {
    $vendor = Vendor::factory()->create();
    $product = Product::factory()->create();
    $clinic = Clinic::factory()->create();
    $order = Order::factory()->create(['clinic_id' => $clinic->id]);
    $gpoAccount = GpoAccount::factory()->create();

    $productOffer = ProductOffer::factory()->create([
        'vendor_id' => $vendor->id,
        'product_id' => $product->id,
        'name' => 'Product Without Size/UOM',
        'size' => null,
        'unit_of_measure' => null,
    ]);

    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $vendor->id,
        'type' => PromotionType::BuyXGetY,
    ]);

    $promotion->productOffers()->attach($productOffer->id);

    $rule = PromotionRule::factory()->create([
        'promotion_id' => $promotion->id,
        'priority' => 1,
    ]);

    PromotionCondition::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ConditionType::MinimumQuantity,
        'config' => ['quantity' => 1],
    ]);

    PromotionAction::factory()->create([
        'promotion_rule_id' => $rule->id,
        'type' => ActionType::GiveFreeProduct,
        'config' => [
            'free_product_offer_id' => $productOffer->id,
            'quantity' => 1,
        ],
    ]);

    // Create context with cart data
    $context = new Context($clinic->id, [$product->id => 2]);

    $tracker = new OrderPromotionTracker();
    $appliedPromotions = collect([
        [
            'promotion' => $promotion,
            'rule' => $rule,
        ]
    ]);

    $tracker->trackAppliedPromotions($order, $appliedPromotions, $context);

    $orderPromotion = $order->promotions()->first();

    expect($orderPromotion)->not->toBeNull();
    expect($orderPromotion->triggering_items[0]['size'])->toBeNull();
    expect($orderPromotion->triggering_items[0]['unit_of_measure'])->toBeNull();
    expect($orderPromotion->applied_benefits[0]['product_offer']['size'])->toBeNull();
    expect($orderPromotion->applied_benefits[0]['product_offer']['unitOfMeasure'])->toBeNull();
});
