<?php

declare(strict_types=1);

use App\Enums\ProductStockStatus;
use App\Models\Product;
use App\Models\Vendor;
use App\Modules\Promotion\Data\SuggestedProductOfferData;
use App\Modules\Promotion\Data\VendorData;
use Brick\Money\Money;

it('can create SuggestedProductOfferData with unitOfMeasure and size', function () {
    $vendor = Vendor::factory()->create([
        'name' => 'Test Vendor',
        'image_path' => 'test-vendor.png',
    ]);

    $product = Product::factory()->create([
        'name' => 'Test Product',
    ]);

    $vendorData = VendorData::from([
        'id' => $vendor->id,
        'name' => $vendor->name,
        'imageUrl' => asset("storage/{$vendor->image_path}"),
    ]);

    $suggestedOffer = new SuggestedProductOfferData(
        id: 'test-offer-id',
        name: 'Test Product Offer',
        imageUrl: 'https://example.com/image.jpg',
        vendorSku: 'SKU-123',
        price: Money::of(100, 'USD'),
        clinicPrice: Money::of(85, 'USD'),
        stockStatus: ProductStockStatus::IN_STOCK,
        vendor: $vendorData,
        orderItemsSumTotalPrice: 10000,
        increments: 6,
        product: $product,
        isRecommended: true,
        rebatePercent: '15.5',
        size: '500ml',
        unitOfMeasure: 'bottles'
    );

    expect($suggestedOffer->id)->toBe('test-offer-id');
    expect($suggestedOffer->name)->toBe('Test Product Offer');
    expect($suggestedOffer->size)->toBe('500ml');
    expect($suggestedOffer->unitOfMeasure)->toBe('bottles');
    expect($suggestedOffer->increments)->toBe(6);
    expect($suggestedOffer->isRecommended)->toBe(true);
    expect($suggestedOffer->rebatePercent)->toBe('15.5');
});

it('can create SuggestedProductOfferData with null unitOfMeasure and size', function () {
    $vendor = Vendor::factory()->create([
        'name' => 'Test Vendor',
        'image_path' => 'test-vendor.png',
    ]);

    $product = Product::factory()->create([
        'name' => 'Test Product',
    ]);

    $vendorData = VendorData::from([
        'id' => $vendor->id,
        'name' => $vendor->name,
        'imageUrl' => asset("storage/{$vendor->image_path}"),
    ]);

    $suggestedOffer = new SuggestedProductOfferData(
        id: 'test-offer-id',
        name: 'Test Product Offer',
        imageUrl: 'https://example.com/image.jpg',
        vendorSku: 'SKU-123',
        price: Money::of(100, 'USD'),
        clinicPrice: Money::of(85, 'USD'),
        stockStatus: ProductStockStatus::IN_STOCK,
        vendor: $vendorData,
        orderItemsSumTotalPrice: 10000,
        increments: 1,
        product: $product,
        isRecommended: false,
        rebatePercent: null,
        size: null,
        unitOfMeasure: null
    );

    expect($suggestedOffer->id)->toBe('test-offer-id');
    expect($suggestedOffer->name)->toBe('Test Product Offer');
    expect($suggestedOffer->size)->toBeNull();
    expect($suggestedOffer->unitOfMeasure)->toBeNull();
    expect($suggestedOffer->increments)->toBe(1);
    expect($suggestedOffer->isRecommended)->toBe(false);
    expect($suggestedOffer->rebatePercent)->toBeNull();
});

it('can create SuggestedProductOfferData using from method', function () {
    $vendor = Vendor::factory()->create([
        'name' => 'Test Vendor',
        'image_path' => 'test-vendor.png',
    ]);

    $product = Product::factory()->create([
        'name' => 'Test Product',
    ]);

    $vendorData = VendorData::from([
        'id' => $vendor->id,
        'name' => $vendor->name,
        'imageUrl' => asset("storage/{$vendor->image_path}"),
    ]);

    $data = [
        'id' => 'test-offer-id',
        'name' => 'Test Product Offer',
        'imageUrl' => 'https://example.com/image.jpg',
        'vendorSku' => 'SKU-123',
        'price' => Money::of(100, 'USD'),
        'clinicPrice' => Money::of(85, 'USD'),
        'stockStatus' => ProductStockStatus::IN_STOCK,
        'vendor' => $vendorData,
        'orderItemsSumTotalPrice' => 10000,
        'increments' => 12,
        'product' => $product,
        'isRecommended' => true,
        'rebatePercent' => '10.0',
        'size' => '250mg',
        'unitOfMeasure' => 'tablets'
    ];

    $suggestedOffer = SuggestedProductOfferData::from($data);

    expect($suggestedOffer->id)->toBe('test-offer-id');
    expect($suggestedOffer->name)->toBe('Test Product Offer');
    expect($suggestedOffer->size)->toBe('250mg');
    expect($suggestedOffer->unitOfMeasure)->toBe('tablets');
    expect($suggestedOffer->increments)->toBe(12);
    expect($suggestedOffer->isRecommended)->toBe(true);
    expect($suggestedOffer->rebatePercent)->toBe('10.0');
});
